# Local Database Setup Guide

## Prerequisites

1. **Install PostgreSQL**
   - Download from https://www.postgresql.org/download/
   - Or use Docker: `docker run --name postgres-tonsite -e POSTGRES_PASSWORD=password -p 5432:5432 -d postgres:14`

2. **Install Redis**
   - Download from https://redis.io/download
   - Or use Docker: `docker run --name redis-tonsite -p 6379:6379 -d redis:7`

## Database Setup

1. **Create Database**
   ```sql
   -- Connect to PostgreSQL as superuser
   psql -U postgres
   
   -- Create database and user
   CREATE DATABASE tonsite_dev;
   CREATE USER tonsite_user WITH PASSWORD 'password';
   GRANT ALL PRIVILEGES ON DATABASE tonsite_dev TO tonsite_user;
   ```

2. **Run Schema**
   ```bash
   # From the project root
   psql -U postgres -d tonsite_dev -f database/schema.sql
   ```

3. **Verify Setup**
   ```bash
   # Test connection
   psql -U postgres -d tonsite_dev -c "SELECT COUNT(*) FROM users;"
   ```

## Quick Docker Setup (Recommended)

```bash
# Start PostgreSQL and Redis with authentication
docker run --name postgres-tonsite -e POSTGRES_PASSWORD=password -e POSTGRES_DB=tonsite_dev -p 5432:5432 -d postgres:14
docker run --name redis-tonsite --requirepass dev-redis-123 -p 6379:6379 -d redis:7

# Wait a few seconds for containers to start, then run schema
sleep 10
docker exec -i postgres-tonsite psql -U postgres -d tonsite_dev < database/schema.sql

# Note: Admin user is now created automatically on first startup
# No hardcoded credentials in schema for security
```

## Environment Variables

Make sure your backend/.env file has:
```
DATABASE_URL=postgresql://postgres:password@localhost:5432/tonsite_dev
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=dev-redis-123

# Admin configuration (development-friendly)
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=DevAdmin123!
ADMIN_SETUP_REQUIRED=true
```

## First Time Setup

After starting the databases:

```bash
# Copy environment template
cp backend/.env.example backend/.env

# Start the backend (will auto-create admin)
cd backend
npm install
npm run dev

# Admin credentials for development:
# Email: <EMAIL>
# Password: DevAdmin123!
```
